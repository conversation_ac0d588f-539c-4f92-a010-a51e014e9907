aiofiles<25.0.0,>=24.1.0
alembic<2.0.0,>=1.13.3
fastapi<0.116.0,>=0.115.11
httpx>=0.27.0
openai>=1.61.0
python-dotenv<2.0.0,>=1.0.1
psycopg-binary<4.0.0,>=3.2.3
requests<3.0.0,>=2.31.0
tiktoken<0.9.0,>=0.8.0
toml<0.11.0,>=0.10.2
types-requests<3.0.0,>=2.31.0
types-aiofiles<25.0.0,>=24.1.0.20240626
typing-extensions<5.0.0,>=4.12.2
pydantic>=2.10.6
python-json-logger>=3.2.1
filetype>=1.2.0

[core]
aiohttp<4.0.0,>=3.10.10
aioshutil<2.0,>=1.5
aiosqlite<0.21.0,>=0.20.0
anthropic>=0.49.0
apscheduler<4.0.0,>=3.10.4
asyncpg<0.30.0,>=0.29.0
azure-ai-inference<2.0.0,>=1.0.0b8
azure-ai-ml<2.0.0,>=1.24.0
bcrypt<5.0.0,>=4.1.3
beautifulsoup4<5.0.0,>=4.12.3
boto3<2.0.0,>=1.35.17
colorlog<7.0.0,>=6.9.0
docutils<0.22.0,>=0.21.2
epub<0.6.0,>=0.5.2
firecrawl-py>=1.13.5
fsspec<2025.0.0,>=2024.6.0
future<2.0.0,>=1.0.0
google-auth<3.0.0,>=2.37.0
google-auth-oauthlib<2.0.0,>=1.2.1
google-genai<0.7.0,>=0.6.0
gunicorn<22.0.0,>=21.2.0
hatchet-sdk==0.47.0
litellm>=1.69.3
markdown<4.0,>=3.6
mistralai>=1.5.2
msg-parser>=1.2.0
networkx<4.0,>=3.3
numpy<1.29.0,>=1.22.4
olefile<0.48,>=0.47
ollama<0.4.0,>=0.3.1
openpyxl<4.0.0,>=3.1.2
orgparse<0.5.0,>=0.4.20231004
pdf2image>=1.17.0
pillow<12.0.0,>=11.1.0
pillow-heif<0.22.0,>=0.21.0
psutil<7.0.0,>=6.0.0
pydantic[email]<3.0.0,>=2.8.2
pyjwt<3.0.0,>=2.8.0
pynacl<2.0.0,>=1.5.0
pypdf<5.0.0,>=4.2.0
pypdf2<4.0.0,>=3.0.1
python-docx<2.0.0,>=1.1.0
python-multipart<0.0.19,>=0.0.9
python-pptx<2.0.0,>=1.0.1
pyyaml<7.0.0,>=6.0.1
sendgrid<7.0.0,>=6.11.0
mailersend<0.6.0,>=0.5.6
sentry-sdk<3.0.0,>=2.20.0
sqlalchemy<3.0.0,>=2.0.30
striprtf<0.0.29,>=0.0.28
supabase<3.0.0,>=2.15.0
tokenizers==0.19
unstructured-client==0.34.0
uvicorn<0.28.0,>=0.27.0.post1
vecs<0.5.0,>=0.4.0
xlrd<3.0.0,>=2.0.1
